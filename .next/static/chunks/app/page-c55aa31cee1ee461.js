(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{1648:function(e,r,t){Promise.resolve().then(t.bind(t,6030))},6030:function(e,r,t){"use strict";t.d(r,{default:function(){return c}});var n=t(7437),a=t(2265);function l(e){let{value:r,onChange:t,placeholder:a="e.g., A cute owl focusing on a book, vibrant colors",disabled:l=!1}=e;return(0,n.jsxs)("div",{className:"w-full",children:[(0,n.jsx)("label",{htmlFor:"promptInput",className:"block text-gray-700 text-lg font-medium mb-2",children:"Describe your app icon:"}),(0,n.jsx)("input",{id:"promptInput",type:"text",value:r,onChange:e=>t(e.target.value),placeholder:a,disabled:l,className:"w-full border-2 border-slate-300 rounded-xl px-4 py-3 transition-colors duration-200 focus:outline-none focus:border-indigo-600 disabled:bg-gray-100 disabled:cursor-not-allowed"})]})}var s=t(3145);function o(e){let{imageUrl:r,isLoading:t,placeholder:a="Your generated icon will appear here."}=e;return(0,n.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-2xl min-h-[200px] w-full flex justify-center items-center overflow-hidden bg-gray-50",children:t?(0,n.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,n.jsx)("div",{className:"border-4 border-gray-200 border-t-indigo-600 rounded-full w-10 h-10 animate-spin"}),(0,n.jsx)("p",{className:"text-gray-500 text-sm",children:"Generating your icon..."})]}):r?(0,n.jsx)("div",{className:"relative w-full h-full min-h-[200px] flex items-center justify-center p-4",children:(0,n.jsx)(s.default,{src:r,alt:"Generated App Icon",width:300,height:300,className:"max-w-full max-h-full object-contain rounded-xl shadow-lg",priority:!0})}):(0,n.jsx)("p",{className:"text-gray-500 text-center px-4",children:a})})}function i(e){let{message:r}=e;return r.visible?(0,n.jsx)("div",{className:"rounded-xl border px-4 py-3 text-center font-medium transition-all duration-300 ".concat((()=>{switch(r.type){case"error":return"bg-red-100 text-red-800 border-red-200";case"success":return"bg-green-100 text-green-800 border-green-200";default:return"bg-yellow-100 text-yellow-800 border-yellow-200"}})()),role:"alert",children:r.text}):null}function c(){let[e,r]=(0,a.useState)(""),[t,s]=(0,a.useState)(),[c,d]=(0,a.useState)(!1),[u,p]=(0,a.useState)({text:"",type:"info",visible:!1}),x=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";p({text:e,type:r,visible:!0})},h=()=>{p(e=>({...e,visible:!1}))},g=async()=>{h(),d(!0);try{let t=await fetch("/api/enhance-prompt",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userIdea:e})}),n=await t.json();n.success&&n.enhancedPrompt?(r(n.enhancedPrompt),x("Prompt enhanced successfully! Now try generating an icon.","success")):x(n.error||"Could not enhance prompt. Please try again.","error")}catch(e){console.error("Error enhancing prompt:",e),x("Failed to enhance prompt. Please try again.","error")}finally{d(!1)}},m=async()=>{if(!e.trim()){x("Please enter a description for the app icon.","error");return}h(),d(!0),s(void 0);try{let r=await fetch("/api/generate-icon",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:e})}),t=await r.json();t.success&&t.imageUrl?(s(t.imageUrl),x("Icon generated successfully!","success")):x(t.error||"Failed to generate icon. Please try again.","error")}catch(e){console.error("Error generating icon:",e),x("Failed to generate icon. Please try again.","error")}finally{d(!1)}};return(0,n.jsx)("div",{className:"w-full max-w-4xl mx-auto",children:(0,n.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl p-8 space-y-6",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-2",children:"App Icon Generator"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Create stunning app icons with AI"})]}),(0,n.jsx)(l,{value:e,onChange:r,disabled:c}),(0,n.jsxs)("div",{className:"flex gap-4 flex-col sm:flex-row",children:[(0,n.jsx)("button",{onClick:g,disabled:c,className:"flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed min-w-[150px]",children:"✨ Enhance Prompt ✨"}),(0,n.jsx)("button",{onClick:m,disabled:c,className:"flex-1 bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed min-w-[150px]",children:"Generate Icon"})]}),(0,n.jsx)(o,{imageUrl:t,isLoading:c}),(0,n.jsx)(i,{message:u})]})})}}},function(e){e.O(0,[145,971,117,744],function(){return e(e.s=1648)}),_N_E=e.O()}]);