"use strict";(()=>{var e={};e.id=826,e.ids=[826],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7766:(e,t,n)=>{n.r(t),n.d(t,{originalPathname:()=>g,patchFetch:()=>m,requestAsyncStorage:()=>d,routeModule:()=>c,serverHooks:()=>l,staticGenerationAsyncStorage:()=>u});var r={};n.r(r),n.d(r,{POST:()=>p});var a=n(3278),o=n(5002),s=n(4877),i=n(1309);async function p(e){try{let{userIdea:t}=await e.json();t&&""!==t.trim()||(t="fun and engaging app icon for attention span training");let n=process.env.GOOGLE_AI_API_KEY;if(!n)return i.NextResponse.json({success:!1,error:"API key not configured. Please set GOOGLE_AI_API_KEY environment variable."},{status:500});let r=[{role:"user",parts:[{text:`Given the following brief idea for an attention span training app icon, elaborate it into a detailed, creative, and visually descriptive prompt suitable for an image generation AI. Focus on making it fun, engaging, and relevant to attention span improvement.
        Idea: ${t.trim()}
        Elaborated Prompt:`}]}],a=`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${n}`,o=await fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:r})});if(!o.ok){let e=await o.json();throw Error(e.error?.message||"Prompt enhancement failed.")}let s=await o.json();if(!s.candidates||!(s.candidates.length>0)||!s.candidates[0].content||!s.candidates[0].content.parts||!(s.candidates[0].content.parts.length>0))return i.NextResponse.json({success:!1,error:"Could not enhance prompt. Please try again."},{status:500});{let e=s.candidates[0].content.parts[0].text;return i.NextResponse.json({success:!0,enhancedPrompt:e})}}catch(t){console.error("Error enhancing prompt:",t);let e=t instanceof Error?t.message:"Unknown error occurred";return i.NextResponse.json({success:!1,error:`Failed to enhance prompt: ${e}`},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/enhance-prompt/route",pathname:"/api/enhance-prompt",filename:"route",bundlePath:"app/api/enhance-prompt/route"},resolvedPagePath:"/Users/<USER>/src/personal/icon-generator/src/app/api/enhance-prompt/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:u,serverHooks:l}=c,g="/api/enhance-prompt/route";function m(){return(0,s.patchFetch)({serverHooks:l,staticGenerationAsyncStorage:u})}}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[379,833],()=>n(7766));module.exports=r})();