"use strict";(()=>{var e={};e.id=271,e.ids=[271],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5410:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>l,patchFetch:()=>m,requestAsyncStorage:()=>u,routeModule:()=>c,serverHooks:()=>g,staticGenerationAsyncStorage:()=>d});var n={};t.r(n),t.d(n,{POST:()=>p});var s=t(3278),a=t(5002),o=t(4877),i=t(1309);async function p(e){try{let{prompt:r}=await e.json();if(!r||""===r.trim())return i.NextResponse.json({success:!1,error:"Please enter a description for the app icon."},{status:400});let t=process.env.GOOGLE_AI_API_KEY;if(!t)return i.NextResponse.json({success:!1,error:"API key not configured. Please set GOOGLE_AI_API_KEY environment variable."},{status:500});let n={instances:{prompt:r.trim()},parameters:{sampleCount:1}},s=`https://generativelanguage.googleapis.com/v1beta/models/imagen-4.0-generate-preview-06-06:predict?key=${t}`,a=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(!a.ok){let e=await a.json();throw Error(e.error?.message||"Image generation failed.")}let o=await a.json();if(!o.predictions||!(o.predictions.length>0)||!o.predictions[0].bytesBase64Encoded)return i.NextResponse.json({success:!1,error:"No image data received. Please try again with a different prompt."},{status:500});{let e=`data:image/png;base64,${o.predictions[0].bytesBase64Encoded}`;return i.NextResponse.json({success:!0,imageUrl:e})}}catch(r){console.error("Error generating image:",r);let e=r instanceof Error?r.message:"Unknown error occurred";return i.NextResponse.json({success:!1,error:`Failed to generate icon: ${e}`},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/generate-icon/route",pathname:"/api/generate-icon",filename:"route",bundlePath:"app/api/generate-icon/route"},resolvedPagePath:"/Users/<USER>/src/personal/icon-generator/src/app/api/generate-icon/route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:u,staticGenerationAsyncStorage:d,serverHooks:g}=c,l="/api/generate-icon/route";function m(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:d})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[379,833],()=>t(5410));module.exports=n})();